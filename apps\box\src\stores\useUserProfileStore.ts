import { create, StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { blockchain, fula } from '@functionland/react-native-fula';
import { TAccount, TBloxFreeSpace } from '../models';
import { KeyChain } from '../utils';
import { useBloxsStore } from './useBloxsStore';
import { useSettingsStore } from './useSettingsStore';
import { getContractService } from '../contracts/contractService';
import { ethers } from 'ethers';
import { getChainConfigByName } from '../contracts/config';
import { FULA_TOKEN_ABI } from '../contracts/abis';
import NetInfo from '@react-native-community/netinfo';
import axios from 'axios';

type BloxConectionStatus =
  | 'CONNECTED'
  | 'CHECKING'
  | 'DISCONNECTED'
  | 'NO INTERNET'
  | 'NO CLIENT';

interface UserProfileActions {
  setHasHydrated: (isHydrated: boolean) => void;
  setKeyChainValue: (service: KeyChain.Service, value: string) => Promise<void>;
  loadAllCredentials: () => Promise<void>;
  setWalletId: (walletId: string, clearSigniture?: boolean) => Promise<void>;
  setAppPeerId: (peerId: string | undefined) => void;
  setBloxPeerIds: (peerIds: string[] | undefined) => void;
  createAccount: ({ seed }: { seed: string }) => Promise<TAccount>;
  getEarnings: () => Promise<void>;
  getContractRewards: () => Promise<void>; // New contract-based rewards
  claimRewards: (poolId?: string) => Promise<void>; // New claim function
  getBloxSpace: () => Promise<TBloxFreeSpace>;
  logout: () => boolean;
  setFulaIsReady: (value: boolean) => void;
  checkBloxConnection: (
    maxTries?: number,
    waitBetweenRetries?: number
  ) => Promise<boolean>;
  reset: () => void;
  checkFulaReadiness: () => Promise<void>; // New method to update fulaReady
  setFulaReinitCount: (count: number) => void;
  setUseLocalIp: (localIp: string) => void;
}

export interface UserProfileSlice {
  _hasHydrated: boolean;
  peerIds: Record<string, string[]>;
  walletId?: string | undefined;
  /**
   * Password is a phares that user enter to create DID and make signiture
   */
  password?: string | undefined;
  /**
   * signiture is the result of signing password and did secret key by wallet
   */
  signiture?: string | undefined;
  address?: string | undefined;
  fulaPeerId?: string | undefined;
  fulaRoodCID?: string | undefined;
  appPeerId?: string | undefined;
  bloxPeerIds?: string[] | undefined;
  accounts: TAccount[];
  earnings: string;
  activeAccount?: TAccount | undefined;
  bloxSpace: TBloxFreeSpace | undefined;
  fulaIsReady: boolean;
  bloxConnectionStatus: BloxConectionStatus;
  fulaReinitCount: number;
  useLocalIp: string | undefined;
}

// define the initial state
const initialState: UserProfileSlice = {
  _hasHydrated: false,
  peerIds: {},
  bloxPeerIds: [],
  accounts: [],
  earnings: '0.0',
  bloxSpace: undefined,
  fulaIsReady: false,
  bloxConnectionStatus: 'CHECKING',
  appPeerId: undefined,
  fulaRoodCID: undefined,
  fulaPeerId: undefined,
  signiture: undefined,
  password: undefined,
  address: undefined,
  walletId: undefined,
  fulaReinitCount: 0,
  useLocalIp: 'scan',
};

const createUserProfileSlice: StateCreator<
  UserProfileSlice & UserProfileActions,
  [],
  [['zustand/persist', unknown]],
  UserProfileSlice & UserProfileActions
> = (set, get) => ({
  ...initialState,

  setHasHydrated: (isHydrated) => {
    set({ _hasHydrated: isHydrated });
  },
  loadAllCredentials: async () => {
    const password =
      (await KeyChain.load(KeyChain.Service.DIDPassword)) || undefined;
    const fulaPeerId =
      (await KeyChain.load(KeyChain.Service.FULAPeerId)) || undefined;
    const fulaRoodCID =
      (await KeyChain.load(KeyChain.Service.FULARootCID)) || undefined;
    const signiture =
      (await KeyChain.load(KeyChain.Service.Signiture)) || undefined;
    const address =
      (await KeyChain.load(KeyChain.Service.Address)) || undefined;
    set({
      password: password?.password,
      fulaPeerId: fulaPeerId?.password,
      fulaRoodCID: fulaRoodCID?.password,
      signiture: signiture?.password,
      address: address?.password,
    });
  },
  setKeyChainValue: async (service, value) => {
    // ... implementation
  },
  setWalletId: async (walletId, clearSigniture) => {
    if (clearSigniture) {
      await KeyChain.reset(KeyChain.Service.DIDPassword);
      await KeyChain.reset(KeyChain.Service.Signiture);
      await KeyChain.reset(KeyChain.Service.Address);
      set({
        walletId,
        password: undefined,
        signiture: undefined,
        address: undefined,
      });
    } else {
      set({ walletId });
    }
  },
  setAppPeerId: (peerId) => set({ appPeerId: peerId }),
  setBloxPeerIds: (peerIds) => set({ bloxPeerIds: peerIds }),
  createAccount: async ({ seed }) => {
    const { fulaIsReady } = get();
    if (!fulaIsReady) {
      throw new Error('Fula is not ready. Please wait...');
    }
    const accounts = get().accounts;
    const account = await blockchain.createAccount(`/${seed}`);
    set({ accounts: [account, ...accounts] });
    return account;
  },
  getEarnings: async (account?: string) => {
    try {
      if (!account) {
        throw new Error('Account address is required for balance query');
      }
      const selectedChain = useSettingsStore.getState().selectedChain;
      const chainConfig = getChainConfigByName(selectedChain);
      const readOnlyProvider = new ethers.JsonRpcProvider(chainConfig.rpcUrl);
      const tokenContract = new ethers.Contract(
        chainConfig.contracts.fulaToken,
        FULA_TOKEN_ABI,
        readOnlyProvider
      );
      const [balance, decimals] = await Promise.all([
        tokenContract.balanceOf(account),
        tokenContract.decimals(),
      ]);
      const fulaBalance = ethers.formatUnits(balance, decimals);
      set({ earnings: fulaBalance });
    } catch (error: any) {
      console.error('Error getting FULA token balance:', error);
      set({ earnings: 'NaN' });
      throw error;
    }
  },
  getContractRewards: async () => {
    try {
      const selectedChain = useSettingsStore.getState().selectedChain;
      const contractService = getContractService(selectedChain);
      const account = await contractService.getConnectedAccount();
      const totalRewards = await contractService.getTotalRewards(account);
      set({ earnings: totalRewards });
    } catch (error: any) {
      console.error('Error getting contract rewards:', error);
      set({ earnings: 'NaN' });
      throw error;
    }
  },
  claimRewards: async (poolId?: string) => {
    try {
      const selectedChain = useSettingsStore.getState().selectedChain;
      const contractService = getContractService(selectedChain);
      if (!poolId) {
        const account = await contractService.getConnectedAccount();
        const userPool = await contractService.getUserPool(account);
        if (!userPool.poolId || userPool.poolId === '0') {
          throw new Error('User is not in any pool');
        }
        poolId = userPool.poolId;
      }
      await contractService.claimRewards(poolId);
      await get().getContractRewards();
    } catch (error: any) {
      console.error('Error claiming rewards:', error);
      throw error;
    }
  },
  logout: () => {
    throw new Error('Not implemented');
  },
  getBloxSpace: async () => {
    const { fulaIsReady } = get();
    if (!fulaIsReady) {
      throw new Error('Fula is not ready. Please wait...');
    }
    const bloxSpace = await blockchain.bloxFreeSpace();
    set({ bloxSpace: bloxSpace as TBloxFreeSpace });
    return bloxSpace as TBloxFreeSpace;
  },
  setFulaIsReady: (value) => set({ fulaIsReady: value }),
  checkBloxConnection: async (maxTries = 1, waitBetweenRetries = 5) => {
    // ... implementation
    return false;
  },
  setUseLocalIp: (localIp: string) => {
    set({
      useLocalIp: localIp,
    });
  },
  setFulaReinitCount: (count: number) => {
    set({
      fulaReinitCount: count,
    });
  },
  checkFulaReadiness: async (maxAttempts = 3): Promise<void> => {
    let readinessPromise: Promise<void> | null = null;
    if (readinessPromise) {
      console.log('checkFulaReadiness is already running. Waiting...');
      try {
        await Promise.race([
          readinessPromise,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout waiting for readiness')), 5000)
          ),
        ]);
      } catch (error: any) {
        console.error(error.message);
      }
      return;
    }
    readinessPromise = new Promise((resolve, reject) => {
      (async () => {
        try {
          let attempts = 0;
          const checkInterval = 3000;
          const check = async () => {
            const netState = await NetInfo?.fetch();
            if (!netState?.isConnected || !netState?.isInternetReachable) {
              const pingResponse = await axios?.head('https://google.com', {
                timeout: 5000,
              });
              if (pingResponse?.status !== 200) {
                console.log('Internet is not connected, waiting for connection...');
                set({ fulaIsReady: false });
                resolve();
                return;
              }
            }
            const ready = await fula.isReady(false);
            console.log('ready is : ' + ready);
            if (ready || attempts >= maxAttempts) {
              set({ fulaIsReady: ready });
              if (attempts >= maxAttempts && !ready) {
                const currentLocalIp = get().useLocalIp;
                if (currentLocalIp && currentLocalIp !== 'scan' && currentLocalIp !== 'delete') {
                  set({ useLocalIp: 'delete' });
                  console.log(`useLocalIp was updated to "delete" from "${currentLocalIp}"`);
                } else if (!currentLocalIp || currentLocalIp === '') {
                  set({ useLocalIp: 'scan' });
                } else {
                  set((state) => ({ fulaReinitCount: state.fulaReinitCount + 1 }));
                }
                reject('could not initialize fula');
              }
              resolve();
            } else {
              console.log('Fula is not ready yet, retrying...');
              attempts++;
              setTimeout(check, checkInterval);
            }
          };
          await check();
        } catch (error: any) {
          console.error('Error in checkFulaReadiness:', error);
          reject(error);
        } finally {
          readinessPromise = null;
        }
      })();
    });
    return readinessPromise;
  },
  reset: () => set(initialState),
});

export const useUserProfileStore = create<UserProfileSlice & UserProfileActions>()(
  persist(
    createUserProfileSlice,
    {
      name: 'userProfileSlice',
      version: 1,
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        state?.setHasHydrated(true);
      },
      partialize: (state) => ({
        walletId: state.walletId,
        bloxPeerIds: state.bloxPeerIds,
        appPeerId: state.appPeerId,
        accounts: state.accounts,
        activeAccount: state.activeAccount,
        fulaReinitCount: state.fulaReinitCount,
      }),
      migrate: async (persistedState, version) => {
        const { setState } = useBloxsStore;
        try {
          if (version === 0 && persistedState) {
            const userProfile = persistedState as UserProfileSlice;
            const bloxs =
              userProfile?.bloxPeerIds?.reduce((obj, peerId, index) => {
                obj[peerId] = {
                  peerId,
                  name: `Blox Unit #${index}`,
                };
                return obj;
              }, {} as Record<string, { peerId: string; name: string }>) || {};
            setState({ bloxs });
          }
        } catch (error: any) {
          console.log(error);
        }
        return persistedState as any;
      },
    }
  )
);
